// Main Business Management Application
class BusinessManagementApp {
    constructor() {
        this.currentModule = 'movement-type';
        this.dataManagers = {
            'movement-type': new DataManager('movement-type'),
            'movement-type-definition': new DataManager('movement-type-definition'),
            'parts-category': new DataManager('parts-category'),
            'parts-category-definition': new DataManager('parts-category-definition')
        };
        this.selectedItems = new Set();
        this.currentEditId = null;
        this.sidebarCollapsed = false;
        this.init();
    }

    // Initialize the application
    init() {
        this.bindEvents();
        this.setupTheme();
        this.loadModule(this.currentModule);
        this.setupSidebar();
    }

    // Bind all event listeners
    bindEvents() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());

        // Sidebar navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const module = link.dataset.module;
                this.switchModule(module);
            });
        });

        // Sidebar toggle
        document.getElementById('sidebarToggle').addEventListener('click', () => this.toggleSidebar());
        document.getElementById('mobileSidebarToggle').addEventListener('click', () => this.toggleMobileSidebar());

        // Modal events
        document.getElementById('modalClose').addEventListener('click', () => this.hideModal());
        document.getElementById('modalCancel').addEventListener('click', () => this.hideModal());
        document.getElementById('modalSave').addEventListener('click', () => this.saveItem());
        document.getElementById('modalOverlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) this.hideModal();
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }

    // Setup theme
    setupTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        this.updateThemeIcon(savedTheme);
    }

    // Toggle theme
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        this.updateThemeIcon(newTheme);
    }

    // Update theme icon
    updateThemeIcon(theme) {
        const icon = document.querySelector('#themeToggle i');
        const text = document.querySelector('.theme-text');
        if (theme === 'dark') {
            icon.className = 'fas fa-sun';
            if (text) text.textContent = 'Light Mode';
        } else {
            icon.className = 'fas fa-moon';
            if (text) text.textContent = 'Dark Mode';
        }
    }

    // Sidebar management
    setupSidebar() {
        // Set initial active state
        this.updateActiveNavLink();
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('collapsed');
        this.sidebarCollapsed = sidebar.classList.contains('collapsed');
        localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed);
    }

    toggleMobileSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('mobile-open');
    }

    // Module management
    switchModule(module) {
        this.currentModule = module;
        this.selectedItems.clear();
        this.loadModule(module);
        this.updateActiveNavLink();
        this.updatePageTitle(module);

        // Close mobile sidebar if open
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.remove('mobile-open');
    }

    loadModule(module) {
        const contentContainer = document.getElementById('contentContainer');
        const template = document.getElementById(`${module}-template`);

        if (template) {
            contentContainer.innerHTML = template.innerHTML;
            this.bindModuleEvents();
            this.renderTable();
            this.updatePagination();
            this.populateFilterOptions();
        }
    }

    updateActiveNavLink() {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            if (link.dataset.module === this.currentModule) {
                link.classList.add('active');
            }
        });
    }

    updatePageTitle(module) {
        const pageTitle = document.getElementById('pageTitle');
        const titleSpan = pageTitle.querySelector('span');
        const titleIcon = pageTitle.querySelector('i');

        const moduleConfig = {
            'movement-type': { title: 'Movement Type', icon: 'fas fa-exchange-alt' },
            'movement-type-definition': { title: 'Movement Type Definition', icon: 'fas fa-cogs' },
            'parts-category': { title: 'Parts Category', icon: 'fas fa-tags' },
            'parts-category-definition': { title: 'Parts Category Definition', icon: 'fas fa-list-alt' }
        };

        const config = moduleConfig[module];
        if (config) {
            titleSpan.textContent = config.title;
            titleIcon.className = config.icon;
        }
    }

    // Get current data manager
    getCurrentDataManager() {
        return this.dataManagers[this.currentModule];
    }

    // Bind module-specific events
    bindModuleEvents() {
        // Toolbar buttons
        document.querySelectorAll('[data-action="add"]').forEach(btn => {
            btn.addEventListener('click', () => this.showAddModal());
        });

        document.querySelectorAll('[data-action="delete"]').forEach(btn => {
            btn.addEventListener('click', () => this.deleteSelected());
        });

        document.querySelectorAll('[data-action="save"]').forEach(btn => {
            btn.addEventListener('click', () => this.saveData());
        });

        document.querySelectorAll('[data-action="refresh"]').forEach(btn => {
            btn.addEventListener('click', () => this.refreshData());
        });

        document.querySelectorAll('[data-action="advanced-search"]').forEach(btn => {
            btn.addEventListener('click', () => this.toggleAdvancedSearch());
        });

        document.querySelectorAll('[data-action="export"]').forEach(btn => {
            btn.addEventListener('click', () => this.toggleExportMenu());
        });

        // Search functionality
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        }

        const searchClear = document.querySelector('.search-clear');
        if (searchClear) {
            searchClear.addEventListener('click', () => this.clearSearch());
        }

        // Export dropdown
        document.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleExport(e));
        });

        // Table events
        const selectAll = document.querySelector('.select-all');
        if (selectAll) {
            selectAll.addEventListener('change', (e) => this.handleSelectAll(e.target.checked));
        }

        // Pagination
        document.querySelectorAll('[data-action="first-page"]').forEach(btn => {
            btn.addEventListener('click', () => this.goToPage(1));
        });

        document.querySelectorAll('[data-action="prev-page"]').forEach(btn => {
            btn.addEventListener('click', () => this.goToPage(this.getCurrentDataManager().currentPage - 1));
        });

        document.querySelectorAll('[data-action="next-page"]').forEach(btn => {
            btn.addEventListener('click', () => this.goToPage(this.getCurrentDataManager().currentPage + 1));
        });

        document.querySelectorAll('[data-action="last-page"]').forEach(btn => {
            btn.addEventListener('click', () => this.goToLastPage());
        });

        const pageInput = document.querySelector('.pagination-input');
        if (pageInput) {
            pageInput.addEventListener('change', (e) => this.goToPage(parseInt(e.target.value)));
        }

        const pageSizeSelect = document.querySelector('.page-size-select');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => this.changePageSize(parseInt(e.target.value)));
        }
    }

    // Render table
    renderTable() {
        const tableBody = document.querySelector('.table-body');
        if (!tableBody) return;

        const dataManager = this.getCurrentDataManager();
        const data = dataManager.getData();

        if (data.data.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 2rem; color: var(--text-muted);">
                        <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                        No movement types found
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = data.data.map(item => {
            const isEditing = dataManager.isEditing(item.id);
            return `
                <tr class="${isEditing ? 'editing' : ''}" data-id="${item.id}">
                    <td class="checkbox-column">
                        <div class="checkbox-wrapper">
                            <input type="checkbox" class="checkbox row-checkbox" data-id="${item.id}"
                                   ${this.selectedItems.has(item.id) ? 'checked' : ''}>
                        </div>
                    </td>
                    <td class="action-column">
                        <div class="action-buttons">
                            ${isEditing ? `
                                <button class="action-btn save" onclick="app.saveInlineEdit(${item.id})" title="Save">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="action-btn cancel" onclick="app.cancelInlineEdit(${item.id})" title="Cancel">
                                    <i class="fas fa-times"></i>
                                </button>
                            ` : `
                                <button class="action-btn edit" onclick="app.startInlineEdit(${item.id})" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn delete" onclick="app.deleteItem(${item.id})" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            `}
                        </div>
                    </td>
                    <td class="editable-cell" data-field="company" data-id="${item.id}">
                        ${isEditing ?
                            `<input type="text" class="inline-input" value="${this.escapeHtml(item.company)}" data-field="company">` :
                            `${this.escapeHtml(item.company)}<div class="edit-indicator"></div>`
                        }
                    </td>
                    <td class="editable-cell" data-field="description" data-id="${item.id}">
                        ${isEditing ?
                            `<input type="text" class="inline-input" value="${this.escapeHtml(item.description)}" data-field="description">` :
                            `${this.escapeHtml(item.description)}<div class="edit-indicator"></div>`
                        }
                    </td>
                    <td class="editable-cell" data-field="isActive" data-id="${item.id}">
                        ${isEditing ?
                            `<select class="inline-select" data-field="isActive">
                                <option value="Yes" ${item.isActive === 'Yes' ? 'selected' : ''}>Yes</option>
                                <option value="No" ${item.isActive === 'No' ? 'selected' : ''}>No</option>
                            </select>` :
                            `<span class="status-badge ${item.isActive === 'Yes' ? 'active' : 'inactive'}">
                                ${item.isActive}
                            </span><div class="edit-indicator"></div>`
                        }
                    </td>
                </tr>
            `;
        }).join('');

        // Bind row checkbox events
        document.querySelectorAll('.row-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => this.handleRowSelection(e));
        });

        // Bind sortable headers
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => this.handleSort(header.dataset.column));
        });

        // Bind editable cells for double-click editing
        document.querySelectorAll('.editable-cell').forEach(cell => {
            if (!dataManager.isEditing(parseInt(cell.dataset.id))) {
                cell.addEventListener('dblclick', () => {
                    const id = parseInt(cell.dataset.id);
                    this.startInlineEdit(id);
                });
            }
        });

        this.updateDeleteButton();
    }

    // Handle row selection
    handleRowSelection(e) {
        const id = parseInt(e.target.dataset.id);
        if (e.target.checked) {
            this.selectedItems.add(id);
        } else {
            this.selectedItems.delete(id);
        }
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
    }

    // Handle select all
    handleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const id = parseInt(checkbox.dataset.id);
            if (checked) {
                this.selectedItems.add(id);
            } else {
                this.selectedItems.delete(id);
            }
        });
        this.updateDeleteButton();
    }

    // Update select all checkbox
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;

        selectAllCheckbox.checked = checkedCount === rowCheckboxes.length && rowCheckboxes.length > 0;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
    }

    // Update delete button state
    updateDeleteButton() {
        const deleteBtn = document.getElementById('deleteBtn');
        deleteBtn.disabled = this.selectedItems.size === 0;
    }

    // Handle sorting
    handleSort(column) {
        const dataManager = this.getCurrentDataManager();
        const currentSort = dataManager.sortColumn;
        const currentDirection = dataManager.sortDirection;

        let newDirection = 'asc';
        if (currentSort === column && currentDirection === 'asc') {
            newDirection = 'desc';
        }

        dataManager.setSorting(column, newDirection);
        this.renderTable();
        this.updatePagination();
        this.updateSortIcons(column, newDirection);
    }

    // Update sort icons
    updateSortIcons(activeColumn, direction) {
        document.querySelectorAll('.sortable').forEach(header => {
            const icon = header.querySelector('.sort-icon');
            header.classList.remove('sorted');

            if (header.dataset.column === activeColumn) {
                header.classList.add('sorted');
                icon.className = direction === 'asc' ? 'fas fa-sort-up sort-icon' : 'fas fa-sort-down sort-icon';
            } else {
                icon.className = 'fas fa-sort sort-icon';
            }
        });
    }

    // Handle search
    handleSearch(searchTerm) {
        const dataManager = this.getCurrentDataManager();
        dataManager.setSearchFilter(searchTerm);
        this.renderTable();
        this.updatePagination();
        this.selectedItems.clear();
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
    }

    // Clear search
    clearSearch() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.value = '';
            this.handleSearch('');
        }
    }

    // Toggle advanced search
    toggleAdvancedSearch() {
        const panel = document.getElementById('advancedSearchPanel');
        if (panel) {
            panel.classList.toggle('active');
        }
    }

    // Apply advanced filters
    applyAdvancedFilters() {
        const filters = {
            company: document.getElementById('companyFilter')?.value || '',
            description: document.getElementById('descriptionFilter')?.value || '',
            status: document.getElementById('statusFilter')?.value || ''
        };

        const dataManager = this.getCurrentDataManager();
        dataManager.setAdvancedFilters(filters);
        this.renderTable();
        this.updatePagination();
        this.selectedItems.clear();
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
        this.showToast('Filters applied successfully', 'success');
    }

    // Clear all filters
    clearAllFilters() {
        const companyFilter = document.getElementById('companyFilter');
        const descriptionFilter = document.getElementById('descriptionFilter');
        const statusFilter = document.getElementById('statusFilter');
        const searchInput = document.querySelector('.search-input');

        if (companyFilter) companyFilter.value = '';
        if (descriptionFilter) descriptionFilter.value = '';
        if (statusFilter) statusFilter.value = '';
        if (searchInput) searchInput.value = '';

        const dataManager = this.getCurrentDataManager();
        dataManager.clearFilters();
        this.renderTable();
        this.updatePagination();
        this.selectedItems.clear();
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
        this.showToast('Filters cleared', 'success');
    }

    // Populate filter options
    populateFilterOptions() {
        const companyFilter = document.getElementById('companyFilter');
        const descriptionFilter = document.getElementById('descriptionFilter');

        if (!companyFilter || !descriptionFilter) return;

        // Clear existing options
        companyFilter.innerHTML = '<option value="">All Companies</option>';
        descriptionFilter.innerHTML = '<option value="">All Descriptions</option>';

        const dataManager = this.getCurrentDataManager();

        // Populate company options
        const companies = dataManager.getUniqueCompanies();
        companies.forEach(company => {
            const option = document.createElement('option');
            option.value = company;
            option.textContent = company;
            companyFilter.appendChild(option);
        });

        // Populate description options
        const descriptions = dataManager.getUniqueDescriptions();
        descriptions.forEach(description => {
            const option = document.createElement('option');
            option.value = description;
            option.textContent = description;
            descriptionFilter.appendChild(option);
        });
    }

    // Pagination methods
    updatePagination() {
        const dataManager = this.getCurrentDataManager();
        const data = dataManager.getData();
        const paginationInfo = document.querySelector('.pagination-info-text');
        const currentPageInput = document.querySelector('.pagination-input');
        const totalPages = document.querySelector('.total-pages');

        if (!paginationInfo || !currentPageInput || !totalPages) return;

        const startItem = data.totalItems === 0 ? 0 : (data.currentPage - 1) * data.pageSize + 1;
        const endItem = Math.min(data.currentPage * data.pageSize, data.totalItems);

        paginationInfo.textContent = `View ${startItem} - ${endItem} of ${data.totalItems}`;
        currentPageInput.value = data.currentPage;
        currentPageInput.max = data.totalPages;
        totalPages.textContent = data.totalPages;

        // Update button states
        const firstPage = document.querySelector('[data-action="first-page"]');
        const prevPage = document.querySelector('[data-action="prev-page"]');
        const nextPage = document.querySelector('[data-action="next-page"]');
        const lastPage = document.querySelector('[data-action="last-page"]');

        if (firstPage) firstPage.disabled = data.currentPage === 1;
        if (prevPage) prevPage.disabled = data.currentPage === 1;
        if (nextPage) nextPage.disabled = data.currentPage === data.totalPages || data.totalPages === 0;
        if (lastPage) lastPage.disabled = data.currentPage === data.totalPages || data.totalPages === 0;
    }

    goToPage(page) {
        const dataManager = this.getCurrentDataManager();
        dataManager.setPage(page);
        this.renderTable();
        this.updatePagination();
        this.selectedItems.clear();
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
    }

    goToLastPage() {
        const dataManager = this.getCurrentDataManager();
        const data = dataManager.getData();
        this.goToPage(data.totalPages);
    }

    changePageSize(size) {
        const dataManager = this.getCurrentDataManager();
        dataManager.setPageSize(size);
        this.renderTable();
        this.updatePagination();
        this.selectedItems.clear();
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
    }

    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoading() {
        document.getElementById('loadingOverlay').classList.add('active');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
    }

    showToast(message, type = 'success') {
        const toastContainer = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        toastContainer.appendChild(toast);

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Hide and remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toastContainer.removeChild(toast), 300);
        }, 3000);
    }

    // Modal methods
    showAddModal() {
        this.currentEditId = null;
        document.getElementById('modalTitle').textContent = 'Add Movement Type';
        document.getElementById('movementForm').reset();
        this.showModal();
    }

    editItem(id) {
        this.currentEditId = id;
        const dataManager = this.getCurrentDataManager();
        const item = dataManager.getItemById(id);
        if (item) {
            document.getElementById('modalTitle').textContent = 'Edit Movement Type';
            document.getElementById('companyInput').value = item.company;
            document.getElementById('descriptionInput').value = item.description;
            document.getElementById('isActiveInput').value = item.isActive;
            this.showModal();
        }
    }

    showAddModal() {
        this.currentEditId = null;
        document.getElementById('modalTitle').textContent = 'Add Movement Type';
        document.getElementById('movementForm').reset();
        this.showModal();
    }

    showModal() {
        document.getElementById('modalOverlay').classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    hideModal() {
        document.getElementById('modalOverlay').classList.remove('active');
        document.body.style.overflow = '';
        this.currentEditId = null;
    }

    saveItem() {
        const form = document.getElementById('movementForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const formData = {
            company: document.getElementById('companyInput').value.trim(),
            description: document.getElementById('descriptionInput').value.trim(),
            isActive: document.getElementById('isActiveInput').value
        };

        const dataManager = this.getCurrentDataManager();

        if (this.currentEditId) {
            // Update existing item
            dataManager.updateItem(this.currentEditId, formData);
            this.showToast('Item updated successfully', 'success');
        } else {
            // Add new item
            dataManager.addItem(formData);
            this.showToast('Item added successfully', 'success');
        }

        this.hideModal();
        this.renderTable();
        this.updatePagination();
        this.populateFilterOptions();
    }

    deleteItem(id) {
        if (confirm('Are you sure you want to delete this item?')) {
            const dataManager = this.getCurrentDataManager();
            dataManager.deleteItem(id);
            this.selectedItems.delete(id);
            this.renderTable();
            this.updatePagination();
            this.updateSelectAllCheckbox();
            this.updateDeleteButton();
            this.showToast('Item deleted successfully', 'success');
        }
    }

    deleteSelected() {
        if (this.selectedItems.size === 0) return;

        const count = this.selectedItems.size;
        if (confirm(`Are you sure you want to delete ${count} selected item(s)?`)) {
            const dataManager = this.getCurrentDataManager();
            dataManager.deleteItems([...this.selectedItems]);
            this.selectedItems.clear();
            this.renderTable();
            this.updatePagination();
            this.updateSelectAllCheckbox();
            this.updateDeleteButton();
            this.showToast(`${count} item(s) deleted successfully`, 'success');
        }
    }

    saveData() {
        this.showLoading();
        // Simulate save operation
        setTimeout(() => {
            this.hideLoading();
            this.showToast('Data saved successfully', 'success');
        }, 1000);
    }

    refreshData() {
        this.showLoading();
        const dataManager = this.getCurrentDataManager();
        dataManager.refresh().then(() => {
            this.hideLoading();
            this.renderTable();
            this.updatePagination();
            this.selectedItems.clear();
            this.updateSelectAllCheckbox();
            this.updateDeleteButton();
            this.showToast('Data refreshed successfully', 'success');
        });
    }

    // Export functionality
    toggleExportMenu() {
        const dropdown = document.querySelector('.dropdown');
        dropdown.classList.toggle('active');
    }

    handleExport(e) {
        e.preventDefault();
        const format = e.target.dataset.format;
        if (format) {
            this.exportData(format);
        }
        // Close dropdown
        document.querySelector('.dropdown').classList.remove('active');
    }

    exportData(format) {
        try {
            const dataManager = this.getCurrentDataManager();
            const exportData = dataManager.exportData(format);
            this.downloadFile(exportData.content, exportData.filename, exportData.mimeType);
            this.showToast(`Data exported as ${format.toUpperCase()} successfully`, 'success');
        } catch (error) {
            this.showToast('Export failed. Please try again.', 'error');
            console.error('Export error:', error);
        }
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    // Inline editing methods
    startInlineEdit(id) {
        const dataManager = this.getCurrentDataManager();

        // Stop editing other rows
        dataManager.editingRows.forEach(editingId => {
            if (editingId !== id) {
                dataManager.stopEditing(editingId);
            }
        });

        dataManager.startEditing(id);
        this.renderTable();
        this.updatePagination();

        // Focus on the first input field and add keyboard handlers
        setTimeout(() => {
            const firstInput = document.querySelector(`tr[data-id="${id}"] .inline-input, tr[data-id="${id}"] .inline-select`);
            if (firstInput) {
                firstInput.focus();
                if (firstInput.type === 'text') {
                    firstInput.select();
                }
            }

            // Add keyboard event listeners for all inputs in the row
            const allInputs = document.querySelectorAll(`tr[data-id="${id}"] .inline-input, tr[data-id="${id}"] .inline-select`);
            allInputs.forEach(input => {
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.saveInlineEdit(id);
                    } else if (e.key === 'Escape') {
                        e.preventDefault();
                        this.cancelInlineEdit(id);
                    } else if (e.key === 'Tab') {
                        // Allow normal tab behavior for navigation between fields
                        return;
                    }
                });
            });
        }, 100);
    }

    saveInlineEdit(id) {
        const row = document.querySelector(`tr[data-id="${id}"]`);
        if (!row) return;

        const inputs = row.querySelectorAll('.inline-input, .inline-select');
        const updates = {};
        let isValid = true;

        inputs.forEach(input => {
            const field = input.dataset.field;
            const value = input.value.trim();

            if (!value && field !== 'isActive') {
                isValid = false;
                input.style.borderColor = 'var(--danger-color)';
                return;
            }

            updates[field] = value;
            input.style.borderColor = '';
        });

        if (!isValid) {
            this.showToast('Please fill in all required fields', 'error');
            return;
        }

        // Update the item
        const dataManager = this.getCurrentDataManager();
        const updatedItem = dataManager.updateItem(id, updates);
        if (updatedItem) {
            dataManager.stopEditing(id);
            this.renderTable();
            this.updatePagination();
            this.populateFilterOptions();
            this.showToast('Item updated successfully', 'success');
        } else {
            this.showToast('Failed to update item', 'error');
        }
    }

    cancelInlineEdit(id) {
        const dataManager = this.getCurrentDataManager();
        dataManager.stopEditing(id);
        this.renderTable();
        this.updatePagination();
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new BusinessManagementApp();
});
