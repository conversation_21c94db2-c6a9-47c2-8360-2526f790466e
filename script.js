// Main application class
class MovementTypeApp {
    constructor() {
        this.dataManager = new DataManager();
        this.selectedItems = new Set();
        this.currentEditId = null;
        this.init();
    }

    // Initialize the application
    init() {
        this.bindEvents();
        this.setupTheme();
        this.renderTable();
        this.updatePagination();
        this.populateFilterOptions();
    }

    // Bind all event listeners
    bindEvents() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());

        // Toolbar buttons
        document.getElementById('addBtn').addEventListener('click', () => this.showAddModal());
        document.getElementById('deleteBtn').addEventListener('click', () => this.deleteSelected());
        document.getElementById('saveBtn').addEventListener('click', () => this.saveData());
        document.getElementById('refreshBtn').addEventListener('click', () => this.refreshData());

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', (e) => this.handleSearch(e.target.value));
        document.getElementById('searchClear').addEventListener('click', () => this.clearSearch());

        // Advanced search
        document.getElementById('advancedSearchBtn').addEventListener('click', () => this.toggleAdvancedSearch());
        document.getElementById('applyFilters').addEventListener('click', () => this.applyAdvancedFilters());
        document.getElementById('clearFilters').addEventListener('click', () => this.clearAllFilters());

        // Export functionality
        document.getElementById('exportBtn').addEventListener('click', () => this.toggleExportMenu());
        document.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', (e) => this.handleExport(e));
        });

        // Table events
        document.getElementById('selectAll').addEventListener('change', (e) => this.handleSelectAll(e.target.checked));

        // Pagination
        document.getElementById('firstPage').addEventListener('click', () => this.goToPage(1));
        document.getElementById('prevPage').addEventListener('click', () => this.goToPage(this.dataManager.currentPage - 1));
        document.getElementById('nextPage').addEventListener('click', () => this.goToPage(this.dataManager.currentPage + 1));
        document.getElementById('lastPage').addEventListener('click', () => this.goToLastPage());
        document.getElementById('currentPageInput').addEventListener('change', (e) => this.goToPage(parseInt(e.target.value)));
        document.getElementById('pageSizeSelect').addEventListener('change', (e) => this.changePageSize(parseInt(e.target.value)));

        // Modal events
        document.getElementById('modalClose').addEventListener('click', () => this.hideModal());
        document.getElementById('modalCancel').addEventListener('click', () => this.hideModal());
        document.getElementById('modalSave').addEventListener('click', () => this.saveItem());
        document.getElementById('modalOverlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) this.hideModal();
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }

    // Setup theme
    setupTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
        this.updateThemeIcon(savedTheme);
    }

    // Toggle theme
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        this.updateThemeIcon(newTheme);
    }

    // Update theme icon
    updateThemeIcon(theme) {
        const icon = document.querySelector('#themeToggle i');
        icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }

    // Render table
    renderTable() {
        const tableBody = document.getElementById('tableBody');
        const data = this.dataManager.getData();

        if (data.data.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 2rem; color: var(--text-muted);">
                        <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                        No movement types found
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = data.data.map(item => `
            <tr>
                <td class="checkbox-column">
                    <input type="checkbox" class="checkbox row-checkbox" data-id="${item.id}"
                           ${this.selectedItems.has(item.id) ? 'checked' : ''}>
                </td>
                <td class="action-column">
                    <button class="action-btn edit" onclick="app.editItem(${item.id})" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                </td>
                <td class="action-column">
                    <button class="action-btn delete" onclick="app.deleteItem(${item.id})" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
                <td>${this.escapeHtml(item.company)}</td>
                <td>${this.escapeHtml(item.description)}</td>
                <td>
                    <span class="status-badge ${item.isActive === 'Yes' ? 'active' : 'inactive'}">
                        ${item.isActive}
                    </span>
                </td>
            </tr>
        `).join('');

        // Bind row checkbox events
        document.querySelectorAll('.row-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => this.handleRowSelection(e));
        });

        // Bind sortable headers
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => this.handleSort(header.dataset.column));
        });

        this.updateDeleteButton();
    }

    // Handle row selection
    handleRowSelection(e) {
        const id = parseInt(e.target.dataset.id);
        if (e.target.checked) {
            this.selectedItems.add(id);
        } else {
            this.selectedItems.delete(id);
        }
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
    }

    // Handle select all
    handleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const id = parseInt(checkbox.dataset.id);
            if (checked) {
                this.selectedItems.add(id);
            } else {
                this.selectedItems.delete(id);
            }
        });
        this.updateDeleteButton();
    }

    // Update select all checkbox
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const rowCheckboxes = document.querySelectorAll('.row-checkbox');
        const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;

        selectAllCheckbox.checked = checkedCount === rowCheckboxes.length && rowCheckboxes.length > 0;
        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
    }

    // Update delete button state
    updateDeleteButton() {
        const deleteBtn = document.getElementById('deleteBtn');
        deleteBtn.disabled = this.selectedItems.size === 0;
    }

    // Handle sorting
    handleSort(column) {
        const currentSort = this.dataManager.sortColumn;
        const currentDirection = this.dataManager.sortDirection;

        let newDirection = 'asc';
        if (currentSort === column && currentDirection === 'asc') {
            newDirection = 'desc';
        }

        this.dataManager.setSorting(column, newDirection);
        this.renderTable();
        this.updatePagination();
        this.updateSortIcons(column, newDirection);
    }

    // Update sort icons
    updateSortIcons(activeColumn, direction) {
        document.querySelectorAll('.sortable').forEach(header => {
            const icon = header.querySelector('.sort-icon');
            header.classList.remove('sorted');

            if (header.dataset.column === activeColumn) {
                header.classList.add('sorted');
                icon.className = direction === 'asc' ? 'fas fa-sort-up sort-icon' : 'fas fa-sort-down sort-icon';
            } else {
                icon.className = 'fas fa-sort sort-icon';
            }
        });
    }

    // Handle search
    handleSearch(searchTerm) {
        this.dataManager.setSearchFilter(searchTerm);
        this.renderTable();
        this.updatePagination();
        this.selectedItems.clear();
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
    }

    // Clear search
    clearSearch() {
        document.getElementById('searchInput').value = '';
        this.handleSearch('');
    }

    // Toggle advanced search
    toggleAdvancedSearch() {
        const panel = document.getElementById('advancedSearchPanel');
        panel.classList.toggle('active');
    }

    // Apply advanced filters
    applyAdvancedFilters() {
        const filters = {
            company: document.getElementById('companyFilter').value,
            description: document.getElementById('descriptionFilter').value,
            status: document.getElementById('statusFilter').value
        };

        this.dataManager.setAdvancedFilters(filters);
        this.renderTable();
        this.updatePagination();
        this.selectedItems.clear();
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
        this.showToast('Filters applied successfully', 'success');
    }

    // Clear all filters
    clearAllFilters() {
        document.getElementById('companyFilter').value = '';
        document.getElementById('descriptionFilter').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('searchInput').value = '';

        this.dataManager.clearFilters();
        this.renderTable();
        this.updatePagination();
        this.selectedItems.clear();
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
        this.showToast('Filters cleared', 'success');
    }

    // Populate filter options
    populateFilterOptions() {
        const companyFilter = document.getElementById('companyFilter');
        const descriptionFilter = document.getElementById('descriptionFilter');

        // Populate company options
        const companies = this.dataManager.getUniqueCompanies();
        companies.forEach(company => {
            const option = document.createElement('option');
            option.value = company;
            option.textContent = company;
            companyFilter.appendChild(option);
        });

        // Populate description options
        const descriptions = this.dataManager.getUniqueDescriptions();
        descriptions.forEach(description => {
            const option = document.createElement('option');
            option.value = description;
            option.textContent = description;
            descriptionFilter.appendChild(option);
        });
    }

    // Pagination methods
    updatePagination() {
        const data = this.dataManager.getData();
        const paginationInfo = document.getElementById('paginationInfo');
        const currentPageInput = document.getElementById('currentPageInput');
        const totalPages = document.getElementById('totalPages');

        const startItem = data.totalItems === 0 ? 0 : (data.currentPage - 1) * data.pageSize + 1;
        const endItem = Math.min(data.currentPage * data.pageSize, data.totalItems);

        paginationInfo.textContent = `View ${startItem} - ${endItem} of ${data.totalItems}`;
        currentPageInput.value = data.currentPage;
        currentPageInput.max = data.totalPages;
        totalPages.textContent = data.totalPages;

        // Update button states
        document.getElementById('firstPage').disabled = data.currentPage === 1;
        document.getElementById('prevPage').disabled = data.currentPage === 1;
        document.getElementById('nextPage').disabled = data.currentPage === data.totalPages || data.totalPages === 0;
        document.getElementById('lastPage').disabled = data.currentPage === data.totalPages || data.totalPages === 0;
    }

    goToPage(page) {
        this.dataManager.setPage(page);
        this.renderTable();
        this.updatePagination();
        this.selectedItems.clear();
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
    }

    goToLastPage() {
        const data = this.dataManager.getData();
        this.goToPage(data.totalPages);
    }

    changePageSize(size) {
        this.dataManager.setPageSize(size);
        this.renderTable();
        this.updatePagination();
        this.selectedItems.clear();
        this.updateSelectAllCheckbox();
        this.updateDeleteButton();
    }

    // Utility methods
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoading() {
        document.getElementById('loadingOverlay').classList.add('active');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
    }

    showToast(message, type = 'success') {
        const toastContainer = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        toastContainer.appendChild(toast);

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Hide and remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toastContainer.removeChild(toast), 300);
        }, 3000);
    }

    // Modal methods
    showAddModal() {
        this.currentEditId = null;
        document.getElementById('modalTitle').textContent = 'Add Movement Type';
        document.getElementById('movementForm').reset();
        this.showModal();
    }

    editItem(id) {
        this.currentEditId = id;
        const item = this.dataManager.getItemById(id);
        if (item) {
            document.getElementById('modalTitle').textContent = 'Edit Movement Type';
            document.getElementById('companyInput').value = item.company;
            document.getElementById('descriptionInput').value = item.description;
            document.getElementById('isActiveInput').value = item.isActive;
            this.showModal();
        }
    }

    showModal() {
        document.getElementById('modalOverlay').classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    hideModal() {
        document.getElementById('modalOverlay').classList.remove('active');
        document.body.style.overflow = '';
        this.currentEditId = null;
    }

    saveItem() {
        const form = document.getElementById('movementForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const formData = {
            company: document.getElementById('companyInput').value.trim(),
            description: document.getElementById('descriptionInput').value.trim(),
            isActive: document.getElementById('isActiveInput').value
        };

        if (this.currentEditId) {
            // Update existing item
            this.dataManager.updateItem(this.currentEditId, formData);
            this.showToast('Movement type updated successfully', 'success');
        } else {
            // Add new item
            this.dataManager.addItem(formData);
            this.showToast('Movement type added successfully', 'success');
        }

        this.hideModal();
        this.renderTable();
        this.updatePagination();
        this.populateFilterOptions();
    }

    deleteItem(id) {
        if (confirm('Are you sure you want to delete this movement type?')) {
            this.dataManager.deleteItem(id);
            this.selectedItems.delete(id);
            this.renderTable();
            this.updatePagination();
            this.updateSelectAllCheckbox();
            this.updateDeleteButton();
            this.showToast('Movement type deleted successfully', 'success');
        }
    }

    deleteSelected() {
        if (this.selectedItems.size === 0) return;

        const count = this.selectedItems.size;
        if (confirm(`Are you sure you want to delete ${count} selected movement type(s)?`)) {
            this.dataManager.deleteItems([...this.selectedItems]);
            this.selectedItems.clear();
            this.renderTable();
            this.updatePagination();
            this.updateSelectAllCheckbox();
            this.updateDeleteButton();
            this.showToast(`${count} movement type(s) deleted successfully`, 'success');
        }
    }

    saveData() {
        this.showLoading();
        // Simulate save operation
        setTimeout(() => {
            this.hideLoading();
            this.showToast('Data saved successfully', 'success');
        }, 1000);
    }

    refreshData() {
        this.showLoading();
        this.dataManager.refresh().then(() => {
            this.hideLoading();
            this.renderTable();
            this.updatePagination();
            this.selectedItems.clear();
            this.updateSelectAllCheckbox();
            this.updateDeleteButton();
            this.showToast('Data refreshed successfully', 'success');
        });
    }

    // Export functionality
    toggleExportMenu() {
        const dropdown = document.querySelector('.dropdown');
        dropdown.classList.toggle('active');
    }

    handleExport(e) {
        e.preventDefault();
        const format = e.target.dataset.format;
        if (format) {
            this.exportData(format);
        }
        // Close dropdown
        document.querySelector('.dropdown').classList.remove('active');
    }

    exportData(format) {
        try {
            const exportData = this.dataManager.exportData(format);
            this.downloadFile(exportData.content, exportData.filename, exportData.mimeType);
            this.showToast(`Data exported as ${format.toUpperCase()} successfully`, 'success');
        } catch (error) {
            this.showToast('Export failed. Please try again.', 'error');
            console.error('Export error:', error);
        }
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new MovementTypeApp();
});
