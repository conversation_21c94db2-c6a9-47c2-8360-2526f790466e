// Sample data for Movement Types
let movementTypes = [
    {
        id: 1,
        company: 'HCL_AMP',
        description: 'Slow',
        isActive: 'Yes',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15')
    },
    {
        id: 2,
        company: 'HCL_AMP',
        description: 'Medium',
        isActive: 'Yes',
        createdAt: new Date('2024-01-16'),
        updatedAt: new Date('2024-01-16')
    },
    {
        id: 3,
        company: 'HCL_AMP',
        description: 'Fast',
        isActive: 'Yes',
        createdAt: new Date('2024-01-17'),
        updatedAt: new Date('2024-01-17')
    },
    {
        id: 4,
        company: 'TechCorp',
        description: 'Express',
        isActive: 'No',
        createdAt: new Date('2024-01-18'),
        updatedAt: new Date('2024-01-18')
    },
    {
        id: 5,
        company: 'DataSys',
        description: 'Standard',
        isActive: 'Yes',
        createdAt: new Date('2024-01-19'),
        updatedAt: new Date('2024-01-19')
    },
    {
        id: 6,
        company: 'LogiFlow',
        description: 'Priority',
        isActive: 'Yes',
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2024-01-20')
    },
    {
        id: 7,
        company: 'QuickMove',
        description: 'Urgent',
        isActive: 'No',
        createdAt: new Date('2024-01-21'),
        updatedAt: new Date('2024-01-21')
    },
    {
        id: 8,
        company: 'FlexTrans',
        description: 'Economy',
        isActive: 'Yes',
        createdAt: new Date('2024-01-22'),
        updatedAt: new Date('2024-01-22')
    }
];

// Data management functions
class DataManager {
    constructor() {
        this.data = [...movementTypes];
        this.filteredData = [...this.data];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.filters = {
            search: '',
            company: '',
            description: '',
            status: ''
        };
    }

    // Get all data
    getAllData() {
        return [...this.data];
    }

    // Get filtered and paginated data
    getData() {
        return {
            data: this.getPaginatedData(),
            totalItems: this.filteredData.length,
            currentPage: this.currentPage,
            pageSize: this.pageSize,
            totalPages: Math.ceil(this.filteredData.length / this.pageSize)
        };
    }

    // Apply filters
    applyFilters() {
        this.filteredData = this.data.filter(item => {
            const searchMatch = !this.filters.search || 
                Object.values(item).some(value => 
                    value.toString().toLowerCase().includes(this.filters.search.toLowerCase())
                );
            
            const companyMatch = !this.filters.company || 
                item.company.toLowerCase().includes(this.filters.company.toLowerCase());
            
            const descriptionMatch = !this.filters.description || 
                item.description.toLowerCase().includes(this.filters.description.toLowerCase());
            
            const statusMatch = !this.filters.status || 
                item.isActive === this.filters.status;

            return searchMatch && companyMatch && descriptionMatch && statusMatch;
        });

        // Reset to first page when filters change
        this.currentPage = 1;
        
        // Apply sorting if set
        if (this.sortColumn) {
            this.applySorting();
        }
    }

    // Apply sorting
    applySorting() {
        this.filteredData.sort((a, b) => {
            let aValue = a[this.sortColumn];
            let bValue = b[this.sortColumn];

            // Handle different data types
            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (aValue < bValue) {
                return this.sortDirection === 'asc' ? -1 : 1;
            }
            if (aValue > bValue) {
                return this.sortDirection === 'asc' ? 1 : -1;
            }
            return 0;
        });
    }

    // Get paginated data
    getPaginatedData() {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        return this.filteredData.slice(startIndex, endIndex);
    }

    // Set search filter
    setSearchFilter(searchTerm) {
        this.filters.search = searchTerm;
        this.applyFilters();
    }

    // Set advanced filters
    setAdvancedFilters(filters) {
        this.filters = { ...this.filters, ...filters };
        this.applyFilters();
    }

    // Clear all filters
    clearFilters() {
        this.filters = {
            search: '',
            company: '',
            description: '',
            status: ''
        };
        this.applyFilters();
    }

    // Set sorting
    setSorting(column, direction) {
        this.sortColumn = column;
        this.sortDirection = direction;
        this.applySorting();
    }

    // Set page
    setPage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
        }
    }

    // Set page size
    setPageSize(size) {
        this.pageSize = size;
        this.currentPage = 1; // Reset to first page
        this.applyFilters(); // Reapply to update pagination
    }

    // Add new item
    addItem(item) {
        const newId = Math.max(...this.data.map(d => d.id)) + 1;
        const newItem = {
            ...item,
            id: newId,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        this.data.push(newItem);
        this.applyFilters();
        return newItem;
    }

    // Update item
    updateItem(id, updates) {
        const index = this.data.findIndex(item => item.id === id);
        if (index !== -1) {
            this.data[index] = {
                ...this.data[index],
                ...updates,
                updatedAt: new Date()
            };
            this.applyFilters();
            return this.data[index];
        }
        return null;
    }

    // Delete item
    deleteItem(id) {
        const index = this.data.findIndex(item => item.id === id);
        if (index !== -1) {
            const deletedItem = this.data.splice(index, 1)[0];
            this.applyFilters();
            return deletedItem;
        }
        return null;
    }

    // Delete multiple items
    deleteItems(ids) {
        const deletedItems = [];
        ids.forEach(id => {
            const deletedItem = this.deleteItem(id);
            if (deletedItem) {
                deletedItems.push(deletedItem);
            }
        });
        return deletedItems;
    }

    // Get item by ID
    getItemById(id) {
        return this.data.find(item => item.id === id);
    }

    // Export data
    exportData(format = 'csv') {
        const dataToExport = this.filteredData.length > 0 ? this.filteredData : this.data;
        
        switch (format) {
            case 'csv':
                return this.exportToCSV(dataToExport);
            case 'json':
                return this.exportToJSON(dataToExport);
            case 'excel':
                return this.exportToExcel(dataToExport);
            default:
                return this.exportToCSV(dataToExport);
        }
    }

    // Export to CSV
    exportToCSV(data) {
        const headers = ['ID', 'Company', 'Description', 'Is Active', 'Created At', 'Updated At'];
        const csvContent = [
            headers.join(','),
            ...data.map(item => [
                item.id,
                `"${item.company}"`,
                `"${item.description}"`,
                item.isActive,
                item.createdAt.toISOString().split('T')[0],
                item.updatedAt.toISOString().split('T')[0]
            ].join(','))
        ].join('\n');

        return {
            content: csvContent,
            filename: `movement_types_${new Date().toISOString().split('T')[0]}.csv`,
            mimeType: 'text/csv'
        };
    }

    // Export to JSON
    exportToJSON(data) {
        const jsonContent = JSON.stringify(data, null, 2);
        return {
            content: jsonContent,
            filename: `movement_types_${new Date().toISOString().split('T')[0]}.json`,
            mimeType: 'application/json'
        };
    }

    // Export to Excel (simplified - would need a library for full Excel support)
    exportToExcel(data) {
        // For now, return CSV with .xlsx extension
        // In a real application, you'd use a library like SheetJS
        const csvData = this.exportToCSV(data);
        return {
            ...csvData,
            filename: csvData.filename.replace('.csv', '.xlsx'),
            mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        };
    }

    // Get unique values for filters
    getUniqueCompanies() {
        return [...new Set(this.data.map(item => item.company))].sort();
    }

    getUniqueDescriptions() {
        return [...new Set(this.data.map(item => item.description))].sort();
    }

    // Refresh data (simulate API call)
    refresh() {
        return new Promise((resolve) => {
            setTimeout(() => {
                // In a real application, this would fetch from an API
                this.applyFilters();
                resolve(this.getData());
            }, 500);
        });
    }
}

// Create global data manager instance
const dataManager = new DataManager();
