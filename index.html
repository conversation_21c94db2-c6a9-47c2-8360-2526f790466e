<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Movement Type Management</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="page-title">
                    <i class="fas fa-exchange-alt"></i>
                    Movement Type
                </h1>
                <div class="header-actions">
                    <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Toolbar -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <button class="btn btn-primary" id="addBtn">
                        <i class="fas fa-plus"></i>
                        Add
                    </button>
                    <button class="btn btn-danger" id="deleteBtn" disabled>
                        <i class="fas fa-trash"></i>
                        Delete
                    </button>
                    <button class="btn btn-secondary" id="saveBtn">
                        <i class="fas fa-save"></i>
                        Save
                    </button>
                </div>
                <div class="toolbar-right">
                    <button class="btn btn-outline" id="advancedSearchBtn">
                        <i class="fas fa-search-plus"></i>
                        Advanced Search
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-outline dropdown-toggle" id="exportBtn">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                        <div class="dropdown-menu" id="exportMenu">
                            <a href="#" class="dropdown-item" data-format="csv">
                                <i class="fas fa-file-csv"></i>
                                Export as CSV
                            </a>
                            <a href="#" class="dropdown-item" data-format="json">
                                <i class="fas fa-file-code"></i>
                                Export as JSON
                            </a>
                            <a href="#" class="dropdown-item" data-format="excel">
                                <i class="fas fa-file-excel"></i>
                                Export as Excel
                            </a>
                        </div>
                    </div>
                    <button class="btn btn-outline" id="refreshBtn">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="search-container">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" id="searchInput" placeholder="Search movement types...">
                    <button class="search-clear" id="searchClear">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Advanced Search Panel -->
            <div class="advanced-search-panel" id="advancedSearchPanel">
                <div class="advanced-search-content">
                    <h3>Advanced Search</h3>
                    <div class="search-filters">
                        <div class="filter-group">
                            <label for="companyFilter">Company</label>
                            <select id="companyFilter" class="filter-select">
                                <option value="">All Companies</option>
                                <option value="HCL_AMP">HCL_AMP</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="descriptionFilter">Description</label>
                            <select id="descriptionFilter" class="filter-select">
                                <option value="">All Descriptions</option>
                                <option value="Slow">Slow</option>
                                <option value="Medium">Medium</option>
                                <option value="Fast">Fast</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="statusFilter">Status</label>
                            <select id="statusFilter" class="filter-select">
                                <option value="">All Status</option>
                                <option value="Yes">Active</option>
                                <option value="No">Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="search-actions">
                        <button class="btn btn-primary" id="applyFilters">Apply Filters</button>
                        <button class="btn btn-secondary" id="clearFilters">Clear Filters</button>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="table-container">
                <div class="table-wrapper">
                    <table class="data-table" id="dataTable">
                        <thead>
                            <tr>
                                <th class="checkbox-column">
                                    <input type="checkbox" id="selectAll" class="checkbox">
                                </th>
                                <th class="action-column">Edit</th>
                                <th class="action-column">Delete</th>
                                <th class="sortable" data-column="company">
                                    Company
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-column="description">
                                    Description
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                                <th class="sortable" data-column="isActive">
                                    Is Active?
                                    <i class="fas fa-sort sort-icon"></i>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- Data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <div class="pagination-info">
                    <span id="paginationInfo">View 1 - 3 of 3</span>
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" id="firstPage" title="First page">
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button class="pagination-btn" id="prevPage" title="Previous page">
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <span class="pagination-text">Page</span>
                    <input type="number" class="pagination-input" id="currentPageInput" value="1" min="1">
                    <span class="pagination-text">of <span id="totalPages">1</span></span>
                    <button class="pagination-btn" id="nextPage" title="Next page">
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button class="pagination-btn" id="lastPage" title="Last page">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                    <select class="page-size-select" id="pageSizeSelect">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal for Add/Edit -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal">
            <div class="modal-header">
                <h2 id="modalTitle">Add Movement Type</h2>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="movementForm">
                    <div class="form-group">
                        <label for="companyInput">Company</label>
                        <input type="text" id="companyInput" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="descriptionInput">Description</label>
                        <input type="text" id="descriptionInput" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="isActiveInput">Is Active?</label>
                        <select id="isActiveInput" class="form-input" required>
                            <option value="Yes">Yes</option>
                            <option value="No">No</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modalCancel">Cancel</button>
                <button class="btn btn-primary" id="modalSave">Save</button>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="data.js"></script>
    <script src="script.js"></script>
</body>
</html>
