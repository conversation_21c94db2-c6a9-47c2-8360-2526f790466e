<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Management System</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-cube"></i>
                    <span class="logo-text">BizManager</span>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" data-module="movement-type">
                            <i class="fas fa-exchange-alt"></i>
                            <span class="nav-text">Movement Type</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-module="movement-type-definition">
                            <i class="fas fa-cogs"></i>
                            <span class="nav-text">Movement Type Definition</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-module="parts-category">
                            <i class="fas fa-tags"></i>
                            <span class="nav-text">Parts Category</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-module="parts-category-definition">
                            <i class="fas fa-list-alt"></i>
                            <span class="nav-text">Parts Category Definition</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <i class="fas fa-moon"></i>
                    <span class="theme-text">Dark Mode</span>
                </button>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-content">
                    <div class="header-left">
                        <button class="mobile-sidebar-toggle" id="mobileSidebarToggle">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="page-title" id="pageTitle">
                            <i class="fas fa-exchange-alt"></i>
                            <span>Movement Type</span>
                        </h1>
                    </div>
                    <div class="header-actions">
                        <div class="user-menu">
                            <button class="user-avatar">
                                <i class="fas fa-user"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content Container -->
            <div class="content-container" id="contentContainer">
                <!-- Dynamic content will be loaded here -->
            </div>
        </main>

        <!-- Module Templates -->
        <!-- Movement Type Module -->
        <template id="movement-type-template">
            <div class="module-content">
                <!-- Toolbar -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" data-action="add">
                            <i class="fas fa-plus"></i>
                            Add
                        </button>
                        <button class="btn btn-danger" data-action="delete" disabled>
                            <i class="fas fa-trash"></i>
                            Delete
                        </button>
                        <button class="btn btn-secondary" data-action="save">
                            <i class="fas fa-save"></i>
                            Save
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-outline" data-action="advanced-search">
                            <i class="fas fa-search-plus"></i>
                            Advanced Search
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline dropdown-toggle" data-action="export">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                            <div class="dropdown-menu">
                                <a href="#" class="dropdown-item" data-format="csv">
                                    <i class="fas fa-file-csv"></i>
                                    Export as CSV
                                </a>
                                <a href="#" class="dropdown-item" data-format="json">
                                    <i class="fas fa-file-code"></i>
                                    Export as JSON
                                </a>
                                <a href="#" class="dropdown-item" data-format="excel">
                                    <i class="fas fa-file-excel"></i>
                                    Export as Excel
                                </a>
                            </div>
                        </div>
                        <button class="btn btn-outline" data-action="refresh">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                    </div>
                </div>

            <!-- Search Bar -->
            <div class="search-container">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" id="searchInput" placeholder="Search movement types...">
                    <button class="search-clear" id="searchClear">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Advanced Search Panel -->
            <div class="advanced-search-panel" id="advancedSearchPanel">
                <div class="advanced-search-content">
                    <h3>Advanced Search</h3>
                    <div class="search-filters">
                        <div class="filter-group">
                            <label for="companyFilter">Company</label>
                            <select id="companyFilter" class="filter-select">
                                <option value="">All Companies</option>
                                <option value="HCL_AMP">HCL_AMP</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="descriptionFilter">Description</label>
                            <select id="descriptionFilter" class="filter-select">
                                <option value="">All Descriptions</option>
                                <option value="Slow">Slow</option>
                                <option value="Medium">Medium</option>
                                <option value="Fast">Fast</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="statusFilter">Status</label>
                            <select id="statusFilter" class="filter-select">
                                <option value="">All Status</option>
                                <option value="Yes">Active</option>
                                <option value="No">Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="search-actions">
                        <button class="btn btn-primary" id="applyFilters">Apply Filters</button>
                        <button class="btn btn-secondary" id="clearFilters">Clear Filters</button>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="table-container">
                <div class="table-wrapper">
                    <table class="data-table" id="dataTable">
                        <thead>
                            <tr>
                                <th class="checkbox-column">
                                    <div class="checkbox-wrapper">
                                        <input type="checkbox" id="selectAll" class="checkbox">
                                        <label for="selectAll" class="checkbox-label"></label>
                                    </div>
                                </th>
                                <th class="action-column">Actions</th>
                                <th class="sortable" data-column="company">
                                    <div class="header-content">
                                        <span>Company</span>
                                        <i class="fas fa-sort sort-icon"></i>
                                    </div>
                                </th>
                                <th class="sortable" data-column="description">
                                    <div class="header-content">
                                        <span>Description</span>
                                        <i class="fas fa-sort sort-icon"></i>
                                    </div>
                                </th>
                                <th class="sortable" data-column="isActive">
                                    <div class="header-content">
                                        <span>Status</span>
                                        <i class="fas fa-sort sort-icon"></i>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- Data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

                <!-- Pagination -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span class="pagination-info-text">View 1 - 3 of 3</span>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" data-action="first-page" title="First page">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="pagination-btn" data-action="prev-page" title="Previous page">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <span class="pagination-text">Page</span>
                        <input type="number" class="pagination-input" value="1" min="1">
                        <span class="pagination-text">of <span class="total-pages">1</span></span>
                        <button class="pagination-btn" data-action="next-page" title="Next page">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="pagination-btn" data-action="last-page" title="Last page">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                        <select class="page-size-select">
                            <option value="5">5</option>
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                </div>
            </div>
        </template>

        <!-- Movement Type Definition Module -->
        <template id="movement-type-definition-template">
            <div class="module-content">
                <!-- Toolbar -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" data-action="add">
                            <i class="fas fa-plus"></i>
                            Add
                        </button>
                        <button class="btn btn-danger" data-action="delete" disabled>
                            <i class="fas fa-trash"></i>
                            Delete
                        </button>
                        <button class="btn btn-secondary" data-action="save">
                            <i class="fas fa-save"></i>
                            Save
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-outline" data-action="advanced-search">
                            <i class="fas fa-search-plus"></i>
                            Advanced Search
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline dropdown-toggle" data-action="export">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                            <div class="dropdown-menu">
                                <a href="#" class="dropdown-item" data-format="csv">
                                    <i class="fas fa-file-csv"></i>
                                    Export as CSV
                                </a>
                                <a href="#" class="dropdown-item" data-format="json">
                                    <i class="fas fa-file-code"></i>
                                    Export as JSON
                                </a>
                                <a href="#" class="dropdown-item" data-format="excel">
                                    <i class="fas fa-file-excel"></i>
                                    Export as Excel
                                </a>
                            </div>
                        </div>
                        <button class="btn btn-outline" data-action="refresh">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Search movement type definitions...">
                        <button class="search-clear">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Data Table -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="checkbox-column">
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" class="checkbox select-all">
                                        </div>
                                    </th>
                                    <th class="action-column">Actions</th>
                                    <th class="sortable" data-column="description">
                                        <div class="header-content">
                                            <span>Description</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="unmoved">
                                        <div class="header-content">
                                            <span>Unmoved</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="slow">
                                        <div class="header-content">
                                            <span>Slow</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="medium">
                                        <div class="header-content">
                                            <span>Medium</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="fast">
                                        <div class="header-content">
                                            <span>Fast</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="isActive">
                                        <div class="header-content">
                                            <span>Is Active?</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="table-body">
                                <!-- Data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span class="pagination-info-text">View 1 - 1 of 1</span>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" data-action="first-page" title="First page">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="pagination-btn" data-action="prev-page" title="Previous page">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <span class="pagination-text">Page</span>
                        <input type="number" class="pagination-input" value="1" min="1">
                        <span class="pagination-text">of <span class="total-pages">1</span></span>
                        <button class="pagination-btn" data-action="next-page" title="Next page">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="pagination-btn" data-action="last-page" title="Last page">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                        <select class="page-size-select">
                            <option value="5">5</option>
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                </div>
            </div>
        </template>

        <!-- Parts Category Module -->
        <template id="parts-category-template">
            <div class="module-content">
                <!-- Toolbar -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" data-action="add">
                            <i class="fas fa-plus"></i>
                            Add
                        </button>
                        <button class="btn btn-danger" data-action="delete" disabled>
                            <i class="fas fa-trash"></i>
                            Delete
                        </button>
                        <button class="btn btn-secondary" data-action="save">
                            <i class="fas fa-save"></i>
                            Save
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-outline" data-action="advanced-search">
                            <i class="fas fa-search-plus"></i>
                            Advanced Search
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline dropdown-toggle" data-action="export">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                            <div class="dropdown-menu">
                                <a href="#" class="dropdown-item" data-format="csv">
                                    <i class="fas fa-file-csv"></i>
                                    Export as CSV
                                </a>
                                <a href="#" class="dropdown-item" data-format="json">
                                    <i class="fas fa-file-code"></i>
                                    Export as JSON
                                </a>
                                <a href="#" class="dropdown-item" data-format="excel">
                                    <i class="fas fa-file-excel"></i>
                                    Export as Excel
                                </a>
                            </div>
                        </div>
                        <button class="btn btn-outline" data-action="refresh">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Search parts categories...">
                        <button class="search-clear">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Data Table -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="checkbox-column">
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" class="checkbox select-all">
                                        </div>
                                    </th>
                                    <th class="action-column">Actions</th>
                                    <th class="sortable" data-column="company">
                                        <div class="header-content">
                                            <span>Company</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="description">
                                        <div class="header-content">
                                            <span>Description</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="isActive">
                                        <div class="header-content">
                                            <span>Is Active?</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="table-body">
                                <!-- Data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span class="pagination-info-text">View 1 - 10 of 83</span>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" data-action="first-page" title="First page">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="pagination-btn" data-action="prev-page" title="Previous page">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <span class="pagination-text">Page</span>
                        <input type="number" class="pagination-input" value="1" min="1">
                        <span class="pagination-text">of <span class="total-pages">9</span></span>
                        <button class="pagination-btn" data-action="next-page" title="Next page">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="pagination-btn" data-action="last-page" title="Last page">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                        <select class="page-size-select">
                            <option value="5">5</option>
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                </div>
            </div>
        </template>

        <!-- Parts Category Definition Module -->
        <template id="parts-category-definition-template">
            <div class="module-content">
                <!-- Toolbar -->
                <div class="toolbar">
                    <div class="toolbar-left">
                        <button class="btn btn-primary" data-action="add">
                            <i class="fas fa-plus"></i>
                            Add
                        </button>
                        <button class="btn btn-danger" data-action="delete" disabled>
                            <i class="fas fa-trash"></i>
                            Delete
                        </button>
                        <button class="btn btn-secondary" data-action="save">
                            <i class="fas fa-save"></i>
                            Save
                        </button>
                    </div>
                    <div class="toolbar-right">
                        <button class="btn btn-outline" data-action="advanced-search">
                            <i class="fas fa-search-plus"></i>
                            Advanced Search
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-outline dropdown-toggle" data-action="export">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                            <div class="dropdown-menu">
                                <a href="#" class="dropdown-item" data-format="csv">
                                    <i class="fas fa-file-csv"></i>
                                    Export as CSV
                                </a>
                                <a href="#" class="dropdown-item" data-format="json">
                                    <i class="fas fa-file-code"></i>
                                    Export as JSON
                                </a>
                                <a href="#" class="dropdown-item" data-format="excel">
                                    <i class="fas fa-file-excel"></i>
                                    Export as Excel
                                </a>
                            </div>
                        </div>
                        <button class="btn btn-outline" data-action="refresh">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Search parts category definitions...">
                        <button class="search-clear">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Data Table -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="checkbox-column">
                                        <div class="checkbox-wrapper">
                                            <input type="checkbox" class="checkbox select-all">
                                        </div>
                                    </th>
                                    <th class="action-column">Actions</th>
                                    <th class="sortable" data-column="partsCategory">
                                        <div class="header-content">
                                            <span>Parts Category</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="conversionFactor">
                                        <div class="header-content">
                                            <span>Conversion Factor</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="profitValue">
                                        <div class="header-content">
                                            <span>Profit Value</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="netRateFactor">
                                        <div class="header-content">
                                            <span>Net Rate Factor</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                    <th class="sortable" data-column="isActive">
                                        <div class="header-content">
                                            <span>Is Active?</span>
                                            <i class="fas fa-sort sort-icon"></i>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="table-body">
                                <!-- Data will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span class="pagination-info-text">View 1 - 2 of 2</span>
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" data-action="first-page" title="First page">
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="pagination-btn" data-action="prev-page" title="Previous page">
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <span class="pagination-text">Page</span>
                        <input type="number" class="pagination-input" value="1" min="1">
                        <span class="pagination-text">of <span class="total-pages">1</span></span>
                        <button class="pagination-btn" data-action="next-page" title="Next page">
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="pagination-btn" data-action="last-page" title="Last page">
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                        <select class="page-size-select">
                            <option value="5">5</option>
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                </div>
            </div>
        </template>

    <!-- Modal for Add/Edit -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal">
            <div class="modal-header">
                <h2 id="modalTitle">Add Movement Type</h2>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="movementForm">
                    <div class="form-group">
                        <label for="companyInput">Company</label>
                        <input type="text" id="companyInput" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="descriptionInput">Description</label>
                        <input type="text" id="descriptionInput" class="form-input" required>
                    </div>
                    <div class="form-group">
                        <label for="isActiveInput">Is Active?</label>
                        <select id="isActiveInput" class="form-input" required>
                            <option value="Yes">Yes</option>
                            <option value="No">No</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modalCancel">Cancel</button>
                <button class="btn btn-primary" id="modalSave">Save</button>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <script src="data.js"></script>
    <script src="script.js"></script>
</body>
</html>
