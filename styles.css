/* CSS Variables for theming */
:root {
    --primary-color: #1f2937;
    --primary-hover: #111827;
    --primary-light: #6b7280;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;

    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --bg-hover: #f9fafb;
    --bg-sidebar: #ffffff;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;

    --border-color: #e5e7eb;
    --border-hover: #d1d5db;
    --border-focus: #1f2937;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);

    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    --transition-fast: 0.15s ease;
    --transition-normal: 0.2s ease;
    --transition-slow: 0.3s ease;

    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 70px;
}

/* Dark theme */
[data-theme="dark"] {
    --primary-color: #f9fafb;
    --primary-hover: #e5e7eb;
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --bg-hover: #374151;
    --bg-sidebar: #111827;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #374151;
    --border-hover: #4b5563;
    --border-focus: #f9fafb;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: all var(--transition-slow);
    font-feature-settings: 'kern' 1, 'liga' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
}

/* App container */
.app-container {
    min-height: 100vh;
    display: flex;
    position: relative;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--bg-sidebar);
    border-right: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--header-height);
    box-sizing: border-box;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--text-primary);
}

.logo i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logo-text {
    transition: opacity var(--transition-normal);
}

.sidebar.collapsed .logo-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
}

.sidebar-toggle:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
    overflow-y: auto;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-normal);
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

.nav-link.active {
    background-color: var(--bg-tertiary);
    color: var(--primary-color);
    font-weight: 600;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: var(--primary-color);
}

.nav-link i {
    font-size: 1.125rem;
    width: 1.25rem;
    text-align: center;
}

.nav-text {
    transition: opacity var(--transition-normal);
}

.sidebar.collapsed .nav-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .nav-link {
    padding: 0.875rem;
    justify-content: center;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
}

.theme-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.75rem;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 0.875rem;
}

.theme-toggle:hover {
    background-color: var(--bg-hover);
    border-color: var(--border-hover);
    color: var(--text-primary);
}

.theme-text {
    transition: opacity var(--transition-normal);
}

.sidebar.collapsed .theme-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .theme-toggle {
    justify-content: center;
    padding: 0.75rem 0.5rem;
}

/* Main Content Area */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-normal);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.sidebar.collapsed + .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Header */
.header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
    height: var(--header-height);
}

.header-content {
    padding: 0 2rem;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
}

.mobile-sidebar-toggle:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-title i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-menu {
    position: relative;
}

.user-avatar {
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-avatar:hover {
    background-color: var(--bg-hover);
    border-color: var(--border-hover);
    color: var(--text-primary);
}

/* Content Container */
.content-container {
    flex: 1;
    padding: 2rem;
    background-color: var(--bg-secondary);
}

.module-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

/* Toolbar */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    gap: 1rem;
    flex-wrap: wrap;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled::before {
    display: none;
}

.btn:active:not(:disabled) {
    transform: translateY(1px);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

[data-theme="dark"] .btn-primary {
    background-color: var(--text-primary);
    color: var(--bg-primary);
    border-color: var(--text-primary);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: #4b5563;
    border-color: #4b5563;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
    background-color: #dc2626;
    border-color: #dc2626;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background-color: transparent;
    border-color: var(--border-color);
    color: var(--text-secondary);
}

.btn-outline:hover:not(:disabled) {
    background-color: var(--bg-hover);
    border-color: var(--border-hover);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-info {
    background-color: #0ea5e9;
    color: white;
    border-color: #0ea5e9;
}

.btn-info:hover:not(:disabled) {
    background-color: #0284c7;
    border-color: #0284c7;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Dropdown */
.dropdown {
    position: relative;
}

.dropdown-toggle::after {
    content: '';
    margin-left: 0.5rem;
    border: 4px solid transparent;
    border-top-color: currentColor;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--bg-tertiary);
}

/* Search container */
.search-container {
    padding: 1.5rem 2rem;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
}

.search-input-wrapper {
    position: relative;
    max-width: 400px;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.search-clear {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    opacity: 0;
    transition: all 0.2s ease;
}

.search-input:not(:placeholder-shown) + .search-clear {
    opacity: 1;
}

.search-clear:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* Advanced search panel */
.advanced-search-panel {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    margin-bottom: 1.5rem;
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    transition: all 0.3s ease;
}

.advanced-search-panel.active {
    max-height: 300px;
    opacity: 1;
}

.advanced-search-content {
    padding: 1.5rem;
}

.advanced-search-content h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 600;
}

.search-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.filter-select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.search-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Table styles */
.table-container {
    background-color: var(--bg-primary);
    overflow: hidden;
    flex: 1;
}

.table-wrapper {
    overflow-x: hidden;
    width: 100%;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    table-layout: fixed;
}

.data-table th,
.data-table td {
    padding: 1rem 1.25rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.data-table th {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-hover) 100%);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid var(--border-color);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
    width: 100%;
}

.data-table tbody tr {
    transition: all var(--transition-fast);
    position: relative;
}

.data-table tbody tr:hover {
    background-color: var(--bg-hover);
    transform: scale(1.001);
}

.data-table tbody tr.editing {
    background-color: rgba(99, 102, 241, 0.05);
    box-shadow: inset 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.checkbox-column {
    width: 60px;
    text-align: center;
}

.action-column {
    width: 120px;
    text-align: center;
}

/* Text alignment classes */
.text-left {
    text-align: left !important;
}

.text-center {
    text-align: center !important;
}

.text-right {
    text-align: right !important;
}

.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: all var(--transition-normal);
}

.sortable:hover {
    background: linear-gradient(135deg, var(--bg-hover) 0%, var(--bg-tertiary) 100%);
}

.sort-icon {
    opacity: 0.5;
    transition: all var(--transition-normal);
    font-size: 0.75rem;
}

.sortable.sorted .sort-icon {
    opacity: 1;
    color: var(--primary-color);
    transform: scale(1.2);
}

/* Custom checkbox styling */
.checkbox-wrapper {
    position: relative;
    display: inline-block;
}

.checkbox {
    width: 1.125rem;
    height: 1.125rem;
    appearance: none;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    background-color: var(--bg-primary);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
}

.checkbox:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

.checkbox:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    font-size: 0.875rem;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background-color: currentColor;
    opacity: 0.1;
    transform: translate(-50%, -50%);
    transition: all var(--transition-normal);
}

.action-btn:hover::before {
    width: 100%;
    height: 100%;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-btn.edit:hover {
    color: var(--primary-color);
}

.action-btn.delete:hover {
    color: var(--danger-color);
}

.action-btn.save:hover {
    color: var(--success-color);
}

.action-btn.cancel:hover {
    color: var(--warning-color);
}

/* Inline editing styles */
.editable-cell {
    position: relative;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.editable-cell:hover {
    background-color: rgba(99, 102, 241, 0.05);
}

.editable-cell.editing {
    padding: 0.5rem;
}

.inline-input,
.inline-select {
    width: 100%;
    padding: 0.5rem;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-family: inherit;
    transition: all var(--transition-normal);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.inline-input:focus,
.inline-select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* Number input alignment */
.inline-input[type="number"] {
    text-align: right;
}

.edit-indicator {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--primary-color);
    border-radius: 50%;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.editable-cell:hover .edit-indicator {
    opacity: 1;
}

/* Status badge */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.active {
    background-color: rgb(16 185 129 / 0.1);
    color: var(--success-color);
}

.status-badge.inactive {
    background-color: rgb(239 68 68 / 0.1);
    color: var(--danger-color);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    gap: 1rem;
    flex-wrap: wrap;
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination-input {
    width: 60px;
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    text-align: center;
    font-size: 0.875rem;
}

.page-size-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

.modal-body {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* Customize Modal */
.customize-modal {
    max-width: 600px;
}

.customize-section {
    margin-bottom: 2rem;
}

.customize-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.customize-group {
    margin-bottom: 1.5rem;
}

.customize-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
}

.radio-group {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    background: var(--bg-primary);
}

.radio-label:hover {
    border-color: var(--primary-color);
    background: var(--bg-hover);
}

.radio-label input[type="radio"] {
    margin: 0;
}

.radio-label input[type="radio"]:checked + span {
    color: var(--primary-color);
    font-weight: 600;
}

.radio-label:has(input[type="radio"]:checked) {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.color-options {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.color-option {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    border: 2px solid transparent;
    transition: all var(--transition-normal);
    position: relative;
}

.color-option:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.color-option.selected {
    border-color: var(--text-primary);
    transform: scale(1.1);
}

.color-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
}

/* Table customization classes */
.table-header-left .data-table th {
    text-align: left !important;
}

.table-header-left .data-table th .header-content {
    justify-content: flex-start !important;
}

.table-header-center .data-table th {
    text-align: center !important;
}

.table-header-center .data-table th .header-content {
    justify-content: center !important;
}

.table-header-right .data-table th {
    text-align: right !important;
}

.table-header-right .data-table th .header-content {
    justify-content: flex-end !important;
}

/* Sort icon positioning for different alignments */
.table-header-left .data-table th .header-content .sort-icon {
    order: 2;
    margin-left: 0.5rem;
}

.table-header-center .data-table th .header-content .sort-icon {
    order: 2;
    margin-left: 0.5rem;
}

.table-header-right .data-table th .header-content .sort-icon {
    order: 1;
    margin-right: 0.5rem;
    margin-left: 0;
}

.table-header-right .data-table th .header-content span {
    order: 2;
}

.table-data-left .data-table td:not(.checkbox-column):not(.action-column) {
    text-align: left !important;
}

.table-data-center .data-table td:not(.checkbox-column):not(.action-column) {
    text-align: center !important;
}

.table-data-right .data-table td:not(.checkbox-column):not(.action-column) {
    text-align: right !important;
}

/* Override specific column alignments when data alignment is applied */
.table-data-left .data-table td.text-center,
.table-data-left .data-table td.text-right {
    text-align: left !important;
}

.table-data-center .data-table td.text-left,
.table-data-center .data-table td.text-right {
    text-align: center !important;
}

.table-data-right .data-table td.text-left,
.table-data-right .data-table td.text-center {
    text-align: right !important;
}

.table-density-compact .data-table th,
.table-density-compact .data-table td {
    padding: 0.5rem 0.75rem;
}

.table-density-normal .data-table th,
.table-density-normal .data-table td {
    padding: 1rem 1.25rem;
}

.table-density-comfortable .data-table th,
.table-density-comfortable .data-table td {
    padding: 1.5rem 1.5rem;
}

/* Header color themes */
.header-theme-default .data-table th {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-hover) 100%);
    color: var(--text-primary);
}

.header-theme-blue .data-table th {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.header-theme-green .data-table th {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.header-theme-purple .data-table th {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
}

.header-theme-red .data-table th {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.header-theme-orange .data-table th {
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    color: white;
}

.header-theme-dark .data-table th {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    color: white;
}

/* Loading spinner */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--danger-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

/* Responsive design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-sidebar-toggle {
        display: block;
    }
}

@media (max-width: 768px) {
    .content-container {
        padding: 1rem;
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
        padding: 1rem;
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }

    .search-container {
        padding: 1rem;
    }

    .search-filters {
        grid-template-columns: 1fr;
    }

    .pagination-container {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 1rem;
    }

    .modal {
        width: 95%;
        margin: 1rem;
    }

    .toast {
        min-width: 250px;
    }

    .data-table th,
    .data-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .page-title {
        font-size: 1.25rem;
    }

    .header-content {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 1.25rem;
    }

    .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
}
